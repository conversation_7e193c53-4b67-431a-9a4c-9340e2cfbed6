import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/app_colors.dart';
import '../../widgets/onboarding_components.dart';
import '../../providers/data_sync_provider.dart';
import '../../providers/global_sync_state_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../services/database_service.dart';
import '../../services/differential_sync_service.dart';
import '../../utils/logger_utils.dart';

/// 데이터 동기화 페이지 - 서버 데이터를 로컬로 다운로드
class SyncConfirmationScreen extends ConsumerStatefulWidget {
  final VoidCallback onSyncComplete;

  const SyncConfirmationScreen({
    super.key,
    required this.onSyncComplete,
  });

  @override
  ConsumerState<SyncConfirmationScreen> createState() => _SyncConfirmationScreenState();
}

class _SyncConfirmationScreenState extends ConsumerState<SyncConfirmationScreen> {
  static const String _tag = 'SyncConfirmationScreen';

  bool _isLoading = false;
  String _currentStep = '동기화 준비 중...';
  double _progress = 0.0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
    
    // 자동 동기화 제거 - 사용자가 명시적으로 버튼을 눌러야 시작
  }

  /// 실시간 동기화 초기화 확인 및 시작
  Future<void> _checkAndInitializeRealtimeSync() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _currentStep = '서버 데이터 확인 중...';
      _progress = 0.1;
      _errorMessage = null;
    });

    try {
      LoggerUtils.logInfo('실제 동기화 시작', tag: _tag);

      // 전역 동기화 상태 관리
      final globalSyncNotifier = ref.read(globalSyncStateProvider.notifier);
      const operationId = 'sync_confirmation_initial';

      // 동기화 시작 (동시 동기화 방지)
      if (!globalSyncNotifier.startSync(operationId, SyncType.initial)) {
        LoggerUtils.logWarning('다른 동기화가 진행 중 - 동기화 건너뛰기', tag: _tag);
        if (mounted) {
          widget.onSyncComplete();
        }
        return;
      }

      try {
        // 1단계: 스마트 양방향 동기화 실행 (앱 재시작 시와 동일한 로직)
        globalSyncNotifier.updateProgress(operationId, 0.1, '동기화 준비 중...');
        await _performSmartBidirectionalSync(globalSyncNotifier, operationId);

        LoggerUtils.logInfo('동기화 완료', tag: _tag);

        if (mounted) {
          setState(() {
            _currentStep = '동기화 완료!';
            _progress = 1.0;
          });

          // 동기화 완료 플래그 설정 (AppWrapper에서 중복 동기화 방지용)
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('sync_completed_recently', true);

          // 전역 동기화 상태 완료 처리
          globalSyncNotifier.completeSync(operationId);

          // 충분한 시간을 두고 완료 메시지 표시 후 다음 화면으로 이동
          await Future.delayed(const Duration(milliseconds: 1500));

          if (mounted) {
            widget.onSyncComplete();
          }
        }

      } catch (e) {
        LoggerUtils.logError('동기화 실패', tag: _tag, error: e);

        // 전역 동기화 상태 실패 처리
        globalSyncNotifier.failSync(operationId, e.toString());

        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = '동기화 중 오류가 발생했습니다: ${e.toString()}';
          });
        }
      }

    } catch (e) {
      LoggerUtils.logError('동기화 초기화 실패', tag: _tag, error: e);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '동기화 초기화 중 오류가 발생했습니다: ${e.toString()}';
        });
      }
    }
  }

  /// 효율적인 온보딩 동기화 수행 (행사 목록 + 현재 행사만)
  Future<void> _performSmartBidirectionalSync(GlobalSyncStateNotifier globalSyncNotifier, String operationId) async {
    final dataSyncService = ref.read(dataSyncServiceProvider);
    final databaseService = ref.read(databaseServiceProvider);

    try {
      // 1단계: 서버 데이터 존재 확인
      globalSyncNotifier.updateProgress(operationId, 0.1, '서버 데이터 확인 중...');
      final hasServerData = await dataSyncService.hasServerData();

      if (!hasServerData) {
        // 서버에 데이터가 없으면 로컬 데이터를 서버로 업로드
        globalSyncNotifier.updateProgress(operationId, 0.2, '로컬 데이터를 서버로 업로드 중...');
        await _performUploadAllData(globalSyncNotifier, operationId);
      } else {
        // 서버에 데이터가 있으면 효율적인 다운로드 수행
        await _performEfficientDownload(globalSyncNotifier, operationId, databaseService);
      }

    } catch (e) {
      LoggerUtils.logError('효율적인 동기화 수행 중 오류', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 로컬 데이터를 서버로 업로드
  Future<void> _performUploadAllData(GlobalSyncStateNotifier globalSyncNotifier, String operationId) async {
    final dataSyncNotifier = ref.read(dataSyncProvider.notifier);

    await dataSyncNotifier.uploadAllData();

    // 업로드 완료까지 대기
    await _waitForSyncCompletion(globalSyncNotifier, operationId, '업로드');
  }

  /// 효율적인 다운로드 수행 (행사 목록 + 현재 행사만)
  Future<void> _performEfficientDownload(GlobalSyncStateNotifier globalSyncNotifier, String operationId, DatabaseService databaseService) async {
    final diffSyncService = DifferentialSyncService(databaseService);

    // 2단계: 행사 목록만 동기화
    globalSyncNotifier.updateProgress(operationId, 0.3, '행사 목록 동기화 중...');
    await diffSyncService.syncEventsList(
      onProgress: (message) {
        globalSyncNotifier.updateProgress(operationId, 0.5, message);
        if (mounted) {
          setState(() {
            _currentStep = message;
            _progress = 0.5;
          });
        }
      },
      onError: (error) {
        throw Exception('행사 목록 동기화 실패: $error');
      },
    );

    // 3단계: 현재 워크스페이스가 있으면 해당 행사 데이터만 동기화
    final workspaceState = ref.read(workspaceProvider);
    if (workspaceState.hasCurrentWorkspace) {
      final currentEventId = workspaceState.currentWorkspace!.id;
      globalSyncNotifier.updateProgress(operationId, 0.6, '현재 행사 데이터 동기화 중...');

      await diffSyncService.syncCurrentEventData(
        currentEventId,
        onProgress: (message) {
          globalSyncNotifier.updateProgress(operationId, 0.8, message);
          if (mounted) {
            setState(() {
              _currentStep = message;
              _progress = 0.8;
            });
          }
        },
        onError: (error) {
          throw Exception('현재 행사 데이터 동기화 실패: $error');
        },
      );
    }

    globalSyncNotifier.updateProgress(operationId, 1.0, '동기화 완료!');
    if (mounted) {
      setState(() {
        _currentStep = '동기화 완료!';
        _progress = 1.0;
      });
    }
  }

  /// 동기화 완료까지 대기
  Future<void> _waitForSyncCompletion(GlobalSyncStateNotifier globalSyncNotifier, String operationId, String operation) async {
    var currentState = ref.read(dataSyncProvider);
    int maxWaitCount = 300; // 30초 최대 대기 (100ms * 300)
    int waitCount = 0;

    while (currentState.status == SyncStatus.syncing && waitCount < maxWaitCount) {
      await Future.delayed(const Duration(milliseconds: 100));
      currentState = ref.read(dataSyncProvider);
      waitCount++;

      // UI 업데이트
      if (mounted) {
        globalSyncNotifier.updateProgress(operationId, currentState.progress ?? 0.0, currentState.message);
        setState(() {
          _currentStep = currentState.message ?? '$operation 중...';
          _progress = currentState.progress ?? 0.0;
        });
      }
    }

    // 타임아웃 체크
    if (waitCount >= maxWaitCount) {
      throw Exception('$operation 시간이 초과되었습니다');
    }

    // 에러가 발생했다면 예외 던지기
    if (currentState.status == SyncStatus.error) {
      throw Exception(currentState.errorMessage ?? '$operation 중 오류가 발생했습니다');
    }
  }

  /// 실시간 동기화 시작 (수동 시작용)
  Future<void> _startRealtimeSync() async {
    await _checkAndInitializeRealtimeSync();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: OnboardingColors.background,
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: Column(
            children: [
              // 메인 컨텐츠
              Expanded(
                child: OnboardingComponents.buildCard(
                  context: context,
                  child: _buildMainContent(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 통합된 메인 컨텐츠 UI
  Widget _buildMainContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 아이콘
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            gradient: OnboardingColors.primaryGradient,
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: OnboardingColors.primary.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Icon(
            _isLoading ? Icons.sync : Icons.cloud_download_rounded,
            size: 50,
            color: OnboardingColors.textOnPrimary,
          ),
        ),
        
        const SizedBox(height: 32),
        
        // 제목
        Text(
          '서버 데이터 동기화',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: OnboardingColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 16),
        
        // 설명 (동기화 중이 아닐 때만 표시)
        if (!_isLoading) ...[
          Text(
            '다른 기기에서 사용하던 데이터를\n이 기기로 다운로드합니다.',
            style: TextStyle(
              fontSize: 16,
              color: OnboardingColors.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // 와이파이 권장 메시지
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: OnboardingColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: OnboardingColors.warning.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi,
                  color: OnboardingColors.warning,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Wi-Fi 연결을 권장합니다',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: OnboardingColors.textPrimary,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '많은 양의 데이터를 다운로드하므로\nWi-Fi 환경에서 이용하시기 바랍니다.',
                        style: TextStyle(
                          color: OnboardingColors.textSecondary,
                          fontSize: 12,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ] else ...[
          // 동기화 중일 때 진행률 표시
          const SizedBox(height: 24),
          
          // 진행률 표시
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 80,
                height: 80,
                child: CircularProgressIndicator(
                  value: _progress,
                  strokeWidth: 6,
                  backgroundColor: OnboardingColors.secondary,
                  valueColor: AlwaysStoppedAnimation<Color>(OnboardingColors.primary),
                ),
              ),
              Column(
                children: [
                  Text(
                    '${(_progress * 100).toInt()}%',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: OnboardingColors.textPrimary,
                    ),
                  ),
                  Text(
                    '진행중',
                    style: TextStyle(
                      fontSize: 10,
                      color: OnboardingColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 현재 작업 표시
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: OnboardingColors.primaryLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _currentStep,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: OnboardingColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 안내 메시지
          Text(
            '잠시만 기다려주세요.\n앱을 종료하지 마세요.',
            style: TextStyle(
              fontSize: 12,
              color: OnboardingColors.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
        
        const SizedBox(height: 40),
        
        // 동기화 시작 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '동기화 시작',
          onPressed: _isLoading ? null : _startRealtimeSync,
          icon: Icons.download_rounded,
        ),
        
        // 에러 메시지 표시
        if (_errorMessage != null) ...[
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: OnboardingColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: OnboardingColors.error.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline_rounded,
                  color: OnboardingColors.error,
                  size: 28,
                ),
                const SizedBox(height: 8),
                Text(
                  '동기화 오류',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: OnboardingColors.error,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _errorMessage!,
                  style: TextStyle(
                    color: OnboardingColors.textSecondary,
                    fontSize: 12,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                OnboardingComponents.buildPrimaryButton(
                  context: context,
                  text: '다시 시도',
                  onPressed: _startRealtimeSync,
                  icon: Icons.refresh_rounded,
                ),
              ],
            ),
          ),
        ] else if (!_isLoading) ...[
          const SizedBox(height: 16),
          
          // 안내 텍스트
          Text(
            '동기화가 완료되면 자동으로 다음 화면으로 이동합니다.',
            style: TextStyle(
              fontSize: 12,
              color: OnboardingColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
