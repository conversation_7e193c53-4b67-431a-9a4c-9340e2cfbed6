import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../models/product.dart';
import '../../models/category.dart' as model_category;
import '../../utils/currency_utils.dart';
import '../product/register_product_screen.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/product_image.dart';

import '../../utils/device_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/safe_dialog_utils.dart';

/// 성능 최적화를 위한 상수들
class _InventoryTabConstants {
  static const double fontSize16 = 16.0;
  static const double spacing16 = 16.0;
  static const double iconSize = 64.0;
  

}

class InventoryTab extends ConsumerStatefulWidget {
  const InventoryTab({super.key});

  @override
  ConsumerState<InventoryTab> createState() => _InventoryTabState();
}

class _InventoryTabState extends ConsumerState<InventoryTab> with WidgetsBindingObserver {
  // 1. state.items, state.hasMore 등은 state.filteredProducts로 일괄 변경
  // 2. itemCount, 리스트 접근 등도 filteredProducts로 통일
  // 3. 페이징 관련 분기, 주석, 코드(스크롤 이벤트, hasMore, loadNextPage 등) 완전 제거
  // 4. 전체 불러오기 구조에 맞게 UI 단순화
  // 5. 불필요한 ScrollController, _onScroll 등도 제거
  
  // 더 이상 사용하지 않음 - 스켈레톤 로딩 로직 간소화

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Provider 상태 변화 감지 및 자동 새로고침은 build에서 처리
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // currentWorkspace가 설정되어 있을 때만 로딩
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        if (currentWorkspace != null) {
          ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
          ref.read(categoryNotifierProvider.notifier).loadCategories();
        }
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 앱이 포커스될 때 단순화된 데이터 새로고침
      LoggerUtils.logDebug('InventoryTab - 앱 포커스됨, 데이터 새로고침', tag: 'InventoryTab');
      // currentWorkspace가 설정되어 있을 때만 로딩
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
        ref.read(categoryNotifierProvider.notifier).loadCategories();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 4단계 가드: 현재 행사가 null인지 먼저 확인
    final currentWorkspace = ref.watch(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      return const _NoEventSelectedWidget();
    }

    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      return const _NoEventSelectedWidget();
    }

    // Provider 상태 변화 감지 및 자동 새로고침
    ref.listen<ProductState>(
      productNotifierProvider,
      (prev, next) {
        if (mounted) setState(() {});
      },
    );
    final state = ref.watch(productNotifierProvider.notifier).state;
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    final columns = isPortrait
        ? ref.watch(inventoryColumnsPortraitProvider)
        : ref.watch(inventoryColumnsLandscapeProvider);

    LoggerUtils.logDebug('InventoryTab - 페이징 상품 상태: ${state.filteredProducts.length}개 상품', tag: 'InventoryTab');
    LoggerUtils.logDebug('InventoryTab - 로딩 상태: ${state.isLoading}', tag: 'InventoryTab');
    // state.hasMore 관련 코드(예: 디버그 로그, 조건문 등) 완전 제거
    LoggerUtils.logDebug('InventoryTab - 열 수 설정: $columns (${isPortrait ? "세로" : "가로"})', tag: 'InventoryTab');

    // 에러 상태 우선 처리
    if (state.errorMessage != null) {
      return _ErrorStateWidget(errorMessage: state.errorMessage!);
    }

    // 실제 로딩 중일 때만 간단한 로딩 표시
    if (state.isLoading && state.filteredProducts.isEmpty) {
      LoggerUtils.logDebug('InventoryTab - 로딩 중 표시', tag: 'InventoryTab');
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('상품을 불러오는 중...'),
          ],
        ),
      );
    }
    
    // 데이터가 없고 로딩도 안 하는 경우 빈 상태 표시
    if (state.filteredProducts.isEmpty) {
      LoggerUtils.logDebug('InventoryTab - 빈 상태 표시 (로딩 중: ${state.isLoading})', tag: 'InventoryTab');
      return const _EmptyStateWidget();
    }

    // 데이터가 있는 경우 카테고리별 그룹화하여 표시
    LoggerUtils.logDebug('InventoryTab - 상품 목록 표시 (${state.filteredProducts.length}개)', tag: 'InventoryTab');
    return RepaintBoundary(
      child: _buildCategoryGroupedProducts(state.filteredProducts, columns),
    );
  }

  /// 카테고리별로 그룹화된 상품 목록을 빌드
  Widget _buildCategoryGroupedProducts(List<Product> products, int columns) {
    // 카테고리별로 상품 그룹화
    final Map<int, List<Product>> groupedProducts = {};
    
    LoggerUtils.logDebug('인벤토리 - 전체 상품 수: ${products.length}', tag: 'InventoryTab');
    
    for (final product in products) {
      final categoryId = product.categoryId;
      LoggerUtils.logDebug('상품 "${product.name}" - categoryId: $categoryId', tag: 'InventoryTab');
      
      if (!groupedProducts.containsKey(categoryId)) {
        groupedProducts[categoryId] = [];
      }
      groupedProducts[categoryId]!.add(product);
    }

    LoggerUtils.logDebug('그룹화된 카테고리: ${groupedProducts.keys.toList()}', tag: 'InventoryTab');

    // 카테고리 데이터 가져오기
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);
    
    return categoriesAsyncValue.when(
      loading: () => _buildSimpleLoading(),
      error: (error, stackTrace) => _ErrorStateWidget(errorMessage: error.toString()),
      data: (categories) {
        // 모든 카테고리를 정렬 순서에 따라 표시 (상품이 없어도 표시)
        final sortedCategories = [...categories]
          ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
        
        LoggerUtils.logDebug('인벤토리 - 전체 카테고리 수: ${categories.length}', tag: 'InventoryTab');
        for (final category in categories) {
          final productCount = groupedProducts[category.id]?.length ?? 0;
          LoggerUtils.logDebug('카테고리 "${category.name}" (ID: ${category.id}) - 상품 수: $productCount', tag: 'InventoryTab');
        }
        
        return SingleChildScrollView( // 오버플로우 방지를 위한 스크롤 래퍼
          padding: EdgeInsets.all(DeviceUtils.getOptimalCardSpacing(context)),
          child: Column(
            children: sortedCategories.map((category) {
              final categoryProducts = groupedProducts[category.id] ?? []; // 빈 리스트도 허용
              
              return _CategorySection(
                category: category,
                products: categoryProducts,
                columns: columns,
                ref: ref,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  /// 간단한 로딩 상태 표시
  Widget _buildSimpleLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('상품을 불러오는 중...'),
        ],
      ),
    );
  }
}

/// 에러 상태 위젯 (const 최적화)
class _ErrorStateWidget extends StatelessWidget {
  final String errorMessage;
  const _ErrorStateWidget({required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: _InventoryTabConstants.iconSize,
            color: Colors.red,
          ),
          const SizedBox(height: _InventoryTabConstants.spacing16),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: _InventoryTabConstants.fontSize16),
          ),
          const SizedBox(height: _InventoryTabConstants.spacing16),
          ElevatedButton(
            onPressed: null,
            child: const Text('다시 시도'),
          ),
        ],
      ),
    );
  }
}

/// 빈 상태 위젯 (const 최적화)
class _EmptyStateWidget extends StatelessWidget {
  const _EmptyStateWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        '등록된 물건이 없습니다.',
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontFamily: 'Pretendard',
          fontSize: _InventoryTabConstants.fontSize16,
          color: Colors.grey,
        ),
      ),
    );
  }
}

/// 개별 재고 그리드 아이템 - 깜빡임 방지를 위한 별도 위젯
class _InventoryGridItem extends StatelessWidget {
  final Product product;
  final int columns;
  final WidgetRef ref;

  const _InventoryGridItem({
    required this.product,
    required this.columns,
    required this.ref,
  });

  double _getOutOfStockTextSize(int columns) {
    switch (columns) {
      case 1:
        return 28.0;
      case 2:
        return 24.0;
      case 3:
        return 20.0;
      case 4:
        return 16.0;
      default:
        return 14.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOutOfStock = product.quantity <= 0;
    final isSmartphone = DeviceUtils.isSmartphone(context);
    final isTablet = !isSmartphone;

    // 반응형 디자인을 위한 값들
    final borderRadius = isTablet ? 16.0 : 12.0;

    return RepaintBoundary(
      child: Container(
        margin: DeviceUtils.getCardMargin(context, columns),
        decoration: BoxDecoration(
          gradient: isOutOfStock
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  OnboardingColors.secondary.withValues(alpha: 0.3),
                  OnboardingColors.secondaryDark.withValues(alpha: 0.3),
                ],
              )
            : OnboardingColors.cardGradient,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
          child: InkWell(
            borderRadius: BorderRadius.circular(borderRadius),
            onTap: () => _showProductActionDialog(context, product, ref),
            child: Padding(
              padding: EdgeInsets.only(
                left: DeviceUtils.getCardPadding(context, columns).left,
                right: DeviceUtils.getCardPadding(context, columns).right,
                top: DeviceUtils.getCardPadding(context, columns).top,
                bottom: 0.2, // 하단 패딩 더욱 최소화 - 텍스트 아래 공간 축소
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.max, // 카드 전체 높이 사용
                mainAxisAlignment: MainAxisAlignment.start, // 위에서부터 배치
                children: [
                  // 이미지 영역 - 가로 공간 100% 활용하는 정사각형
                  AspectRatio(
                    aspectRatio: 1.0, // 완벽한 정사각형
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                OnboardingColors.surfaceVariant,
                                OnboardingColors.secondary.withValues(alpha: 0.5),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            child: ProductImage(
                              imagePath: product.imagePath ?? '',
                              fit: BoxFit.cover, // 크롭된 이미지 표시
                            ),
                          ),
                        ),
                        // 품절 오버레이
                        if (isOutOfStock)
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    OnboardingColors.error.withValues(alpha: 0.8),
                                    OnboardingColors.errorLight.withValues(alpha: 0.6),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(borderRadius * 0.7),
                              ),
                              child: Center(
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isTablet ? 16.0 : 12.0,
                                    vertical: isTablet ? 8.0 : 6.0,
                                  ),
                                  decoration: BoxDecoration(
                                    color: OnboardingColors.darkBrown.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                  ),
                                  child: Text(
                                    '품절',
                                    style: TextStyle(
                                      color: OnboardingColors.textOnDark,
                                      fontSize: _getOutOfStockTextSize(columns),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ),
                          ),
                        ),

                        // 재고 수량 배지 (우측 하단) - 항상 표시
                        if (product.quantity > 0)
                          Positioned(
                            right: DeviceUtils.getQuantityBadgePosition(context, columns).right,
                            bottom: DeviceUtils.getQuantityBadgePosition(context, columns).bottom,
                            child: Container(
                              width: DeviceUtils.getQuantityBadgeSize(context, columns),
                              height: DeviceUtils.getQuantityBadgeSize(context, columns),
                              decoration: BoxDecoration(
                                gradient: isOutOfStock
                                  ? LinearGradient(
                                      colors: [
                                        OnboardingColors.error.withValues(alpha: 0.8),
                                        OnboardingColors.errorLight.withValues(alpha: 0.6),
                                      ],
                                    )
                                  : LinearGradient(
                                      colors: [
                                        OnboardingColors.primary.withValues(alpha: 0.9), // 주황색 계열
                                        OnboardingColors.primaryDark.withValues(alpha: 0.9), // 진한 주황색
                                      ],
                                    ),
                                borderRadius: BorderRadius.circular(
                                  DeviceUtils.getQuantityBadgeBorderRadius(context, columns), // 라운드 사각형
                                ),
                                border: Border.all(
                                  color: OnboardingColors.surface,
                                  width: 1.5, // 2.0에서 1.5로 테두리 굵기 감소
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  product.quantity > 99 ? '99+' : '${product.quantity}',
                                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                                    fontFamily: 'Pretendard',
                                    color: OnboardingColors.textOnPrimary,
                                    fontSize: DeviceUtils.getQuantityBadgeTextSize(context, columns),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // 가격 - 이미지 바로 아래로 이동
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(
                      top: DeviceUtils.getImageTextSpacing(context, columns), // 열 수에 따라 동적 조정
                    ),
                    padding: DeviceUtils.getPriceQuantityPadding(context, columns),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFFA0D8A0).withValues(alpha: 0.1), // 연한 초록색
                          const Color(0xFF8FCC8F).withValues(alpha: 0.1), // 조금 더 진한 연한 초록색
                        ],
                      ),
                      borderRadius: BorderRadius.circular(borderRadius * 0.3),
                    ),
                    child: Text(
                      CurrencyUtils.formatCurrency(product.price) + '원',
                      style: Theme.of(context).textTheme.titleSmall!.copyWith(
                        fontFamily: 'Pretendard',
                        fontSize: DeviceUtils.getUnifiedTextSize(context, columns),
                        color: const Color(0xFF4CAF50), // 초록색으로 변경
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  SizedBox(height: DeviceUtils.getComponentSpacing(context, columns)), // 추가 간격 제거하여 더욱 컴팩트하게

                  // 상품명 - 가격 아래로 이동 (2줄까지 최적화된 높이) - Expanded로 감싸서 남은 공간 활용
                  Expanded(
                    child: Container(
                      child: Align(
                        alignment: Alignment(0.0, -0.7), // 상단에서 살짝 아래로 텍스트 배치
                        child: Text(
                          product.name,
                          maxLines: 2, // 2줄 고정
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center, // 중앙 정렬
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: 'Pretendard',
                            fontSize: DeviceUtils.getUnifiedTextSize(context, columns),
                            color: isOutOfStock
                              ? OnboardingColors.textSecondary
                              : OnboardingColors.textPrimary,
                            fontWeight: FontWeight.w600,
                            height: 1.2,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 상품 액션 다이얼로그
  void _showProductActionDialog(BuildContext context, Product product, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Padding(
          padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 아이콘과 제목을 한 줄로 배치
              Row(
                children: [
                  custom_dialog.DialogTheme.buildCompactIconContainer(
                    icon: Icons.inventory_2_rounded,
                    color: OnboardingColors.primary,
                    isTablet: isTablet,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Text(
                      product.name,
                      style: custom_dialog.DialogTheme.titleStyle.copyWith(
                        fontSize: isTablet ? 20.0 : 18.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 상품 정보 (더 컴팩트한 그리드 레이아웃)
              Container(
                padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      OnboardingColors.surfaceVariant,
                      OnboardingColors.secondary.withValues(alpha: 0.3),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // 첫 번째 행: 판매자, 가격
                    Row(
                      children: [
                        Expanded(child: _buildCompactInfoItem('판매자', product.sellerName ?? '미지정', isTablet)),
                        SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                        Expanded(child: _buildCompactInfoItem('가격', '${CurrencyUtils.formatCurrency(product.price)}원', isTablet)),
                      ],
                    ),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                    // 두 번째 행: 재고, 등록일
                    Row(
                      children: [
                        Expanded(child: _buildCompactInfoItem('재고', '${product.quantity}개', isTablet)),
                        SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                        // 등록일 필드 제거됨
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

              // 버튼들 (더 컴팩트한 레이아웃)
              Row(
                children: [
                  // 닫기 버튼
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: OnboardingColors.secondary),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              vertical: isTablet ? 12.0 : 10.0,
                            ),
                            child: Text(
                              '닫기',
                              style: TextStyle(
                                color: OnboardingColors.textSecondary,
                                fontSize: isTablet ? 14.0 : 13.0,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),

                  // 수정 버튼
                  Expanded(
                    child: custom_dialog.DialogTheme.buildGradientButton(
                      decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                      isCompact: true,
                      onPressed: () async {
                        SafeDialogUtils.safePopDialog(context);
                        final result = await SafeDialogUtils.safePushPage(
                          context,
                          RegisterProductScreen(product: product, isEditing: true),
                          logContext: 'Edit Product: ${product.name}',
                        );
                        if (result == true && context.mounted) {
                          await ref.read(productNotifierProvider.notifier).loadProducts();
                        }
                      },
                      child: Text(
                        '수정',
                        style: TextStyle(
                          color: OnboardingColors.textOnPrimary,
                          fontSize: isTablet ? 14.0 : 13.0,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),

                  // 삭제 버튼
                  Expanded(
                    child: custom_dialog.DialogTheme.buildGradientButton(
                      decoration: custom_dialog.DialogTheme.deleteButtonDecoration,
                      isCompact: true,
                      onPressed: () async {
                        SafeDialogUtils.safePopDialog(context);
                        final shouldDelete = await _showDeleteConfirmDialog(context, product);
                        if (shouldDelete && context.mounted) {
                          await _deleteProduct(context, product, ref);
                          await ref.read(productNotifierProvider.notifier).loadProducts();
                        }
                      },
                      child: Text(
                        '삭제',
                        style: TextStyle(
                          color: OnboardingColors.textOnDark,
                          fontSize: isTablet ? 14.0 : 13.0,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 컴팩트한 정보 아이템 위젯 빌더
  Widget _buildCompactInfoItem(String label, String value, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: OnboardingColors.textSecondary,
            fontSize: isTablet ? 12.0 : 11.0,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            color: OnboardingColors.textPrimary,
            fontSize: isTablet ? 14.0 : 13.0,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 상품 삭제 확인 다이얼로그
  Future<bool> _showDeleteConfirmDialog(
    BuildContext context,
    Product product,
  ) async {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return await showDialog<bool>(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Padding(
          padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 아이콘과 제목을 한 줄로 배치
              Row(
                children: [
                  custom_dialog.DialogTheme.buildCompactIconContainer(
                    icon: Icons.warning_rounded,
                    color: OnboardingColors.error,
                    isTablet: isTablet,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Text(
                      '상품 삭제',
                      style: custom_dialog.DialogTheme.titleStyle.copyWith(
                        fontSize: isTablet ? 20.0 : 18.0,
                        color: OnboardingColors.error,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 내용 (더 컴팩트)
              Container(
                padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      OnboardingColors.error.withValues(alpha: 0.1),
                      OnboardingColors.errorLight.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      '\'${product.name}\' 상품을 삭제하시겠습니까?',
                      style: custom_dialog.DialogTheme.contentStyle.copyWith(
                        fontSize: isTablet ? 16.0 : 14.0,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                    Text(
                      '이 작업은 되돌릴 수 없습니다.',
                      style: custom_dialog.DialogTheme.contentStyle.copyWith(
                        fontSize: isTablet ? 14.0 : 12.0,
                        color: OnboardingColors.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

              // 버튼들 (더 컴팩트)
              Row(
                children: [
                  // 취소 버튼
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: OnboardingColors.secondary),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.of(context).pop(false),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              vertical: isTablet ? 14.0 : 12.0,
                            ),
                            child: Text(
                              '취소',
                              style: TextStyle(
                                color: OnboardingColors.textSecondary,
                                fontSize: isTablet ? 16.0 : 14.0,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),

                  // 삭제 버튼
                  Expanded(
                    child: custom_dialog.DialogTheme.buildGradientButton(
                      decoration: custom_dialog.DialogTheme.deleteButtonDecoration,
                      isCompact: true,
                      onPressed: () => Navigator.of(context).pop(true),
                      child: Text(
                        '삭제',
                        style: TextStyle(
                          color: OnboardingColors.textOnDark,
                          fontSize: isTablet ? 16.0 : 14.0,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Pretendard',
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ) ?? false;
  }

  /// 상품 삭제 처리
  Future<void> _deleteProduct(
    BuildContext context,
    Product product,
    WidgetRef ref,
  ) async {
    try {
      await ref.read(productNotifierProvider.notifier).deleteProduct(product);
      
      if (context.mounted) {
        ToastUtils.showSuccess(context, '\'${product.name}\' 상품이 삭제되었습니다.');
      }
    } catch (e) {
      if (context.mounted) {
        ToastUtils.showError(context, '상품 삭제 중 오류가 발생했습니다: $e');
      }
    }
  }
}

/// 카테고리별 상품 섹션 위젯
class _CategorySection extends StatelessWidget {
  final model_category.Category category;
  final List<Product> products;
  final int columns;
  final WidgetRef ref;

  const _CategorySection({
    required this.category,
    required this.products,
    required this.columns,
    required this.ref,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 카테고리 제목 - Material Design 3 Chips 스타일 (상품과 정렬 맞춤)
        Container(
          margin: EdgeInsets.only(
            left: DeviceUtils.getOptimalCardSpacing(context), // 상품 카드와 정렬 맞춤
            right: 16.0, 
            bottom: 8.0
          ), // 아래 간격 줄임
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                decoration: BoxDecoration(
                  color: OnboardingColors.primary.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(
                    DeviceUtils.isSmartphone(context) ? 12.0 : 16.0, // 상품 카드와 동일한 라운드
                  ),
                  border: Border.all(
                    color: OnboardingColors.primary.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 카테고리 아이콘
                    Icon(
                      Icons.folder_outlined,
                      size: 18.0,
                      color: OnboardingColors.primary.withValues(alpha: 0.9),
                    ),
                    const SizedBox(width: 8.0),
                    Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 14.0,
                        fontWeight: FontWeight.bold,
                        color: OnboardingColors.primary.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // 카테고리 내 상품들 - 빈 카테고리도 처리
        if (products.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            margin: const EdgeInsets.only(bottom: 16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: const Text(
              '이 카테고리에는 등록된 상품이 없습니다.',
              style: TextStyle(
                fontSize: 14.0,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 24.0), // 카테고리 간 간격 늘림
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              crossAxisSpacing: DeviceUtils.getOptimalCardSpacing(context),
              mainAxisSpacing: DeviceUtils.getOptimalCardSpacing(context),
              childAspectRatio: DeviceUtils.getOptimalCardAspectRatio(context, columns),
            ),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return _InventoryGridItem(
                product: product,
                columns: columns,
                ref: ref,
              );
            },
          ),
      ],
    );
  }
}

/// 행사가 선택되지 않았을 때 표시되는 안내 위젯
class _NoEventSelectedWidget extends StatelessWidget {
  const _NoEventSelectedWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.event_busy, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            '행사를 선택해주세요',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            '왼쪽 상단 메뉴에서 행사를 선택하거나\n새 행사를 생성할 수 있습니다.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(Icons.menu),
            label: const Text('메뉴 열기'),
          ),
        ],
      ),
    );
  }
}
