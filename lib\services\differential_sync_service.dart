/// 차분 동기화 서비스
/// 
/// Firebase와 로컬 데이터를 비교하여 변경된 부분만 동기화합니다.
/// DB 사용량을 최소화하고 효율적인 동기화를 제공합니다.
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/seller.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../services/database_service.dart';
import '../services/sync_metadata_service.dart';
import '../utils/logger_utils.dart';

/// 차분 동기화 결과
class DiffSyncResult {
  final int downloaded;
  final int uploaded;
  final int deleted;
  final int skipped;
  final List<String> errors;
  
  const DiffSyncResult({
    required this.downloaded,
    required this.uploaded,
    required this.deleted,
    required this.skipped,
    required this.errors,
  });
  
  int get totalProcessed => downloaded + uploaded + deleted + skipped;
  bool get hasErrors => errors.isNotEmpty;
}

class DifferentialSyncService {
  static const String _tag = 'DifferentialSyncService';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DatabaseService _databaseService;
  final SyncMetadataService _metadataService = SyncMetadataService.instance;

  DifferentialSyncService(this._databaseService);

  /// 행사 목록 동기화 (메타데이터만)
  Future<DiffSyncResult> syncEventsList({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('사용자 인증이 필요합니다');
    }

    try {
      LoggerUtils.logInfo('행사 목록 동기화 시작', tag: _tag);
      onProgress?.call('행사 목록 동기화 중...');

      // Firebase에서 행사 목록 가져오기
      final remoteEvents = await _getRemoteEvents(user.uid);

      // 로컬 행사 목록 가져오기
      final localEvents = await _getLocalEvents();

      int downloaded = 0;
      int deleted = 0;
      final List<String> errors = [];

      // 삭제된 행사 처리
      for (final localEvent in localEvents) {
        final isDeleted = !remoteEvents.any((remote) => remote.id == localEvent.id);
        if (isDeleted) {
          try {
            LoggerUtils.logInfo('행사 삭제: ${localEvent.name} (ID: ${localEvent.id})', tag: _tag);
            await _databaseService.deleteEventAndAllData(localEvent.id!);
            deleted++;
          } catch (e) {
            errors.add('행사 ${localEvent.name} 삭제 실패: $e');
          }
        }
      }

      // 새로운/업데이트된 행사 처리
      for (final remoteEvent in remoteEvents) {
        try {
          await _databaseService.insertOrUpdateEvent(remoteEvent);
          downloaded++;
        } catch (e) {
          errors.add('행사 ${remoteEvent.name} 동기화 실패: $e');
        }
      }

      LoggerUtils.logInfo('행사 목록 동기화 완료 - 다운로드: $downloaded, 삭제: $deleted', tag: _tag);

      return DiffSyncResult(
        downloaded: downloaded,
        uploaded: 0,
        deleted: deleted,
        skipped: 0,
        errors: errors,
      );
    } catch (e) {
      LoggerUtils.logError('행사 목록 동기화 실패', tag: _tag, error: e);
      onError?.call('행사 목록 동기화 실패: $e');
      rethrow;
    }
  }

  /// 현재 행사의 데이터만 차분 동기화
  Future<DiffSyncResult> syncCurrentEventData(
    int eventId, {
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('로그인이 필요합니다');
    }
    
    try {
      LoggerUtils.logInfo('행사 $eventId 차분 동기화 시작', tag: _tag);
      onProgress?.call('동기화 준비 중...');
      
      int totalDownloaded = 0;
      int totalUploaded = 0;
      int totalDeleted = 0;
      int totalSkipped = 0;
      final List<String> errors = [];
      
      // 동기화할 컬렉션들 (우선순위 순)
      final collections = [
        'categories',
        'products', 
        'sellers',
        'prepayments',
        'prepayment_virtual_products',
        'prepayment_product_links',
        'sales_logs',
      ];
      
      for (int i = 0; i < collections.length; i++) {
        final collection = collections[i];
        final progress = ((i + 1) / collections.length * 100).toInt();
        
        try {
          onProgress?.call('$collection 동기화 중... ($progress%)');
          
          final result = await _syncCollection(user.uid, eventId, collection);
          totalDownloaded += result.downloaded;
          totalUploaded += result.uploaded;
          totalDeleted += result.deleted;
          totalSkipped += result.skipped;
          errors.addAll(result.errors);
          
          LoggerUtils.logInfo(
            '$collection 동기화 완료: 다운로드=${result.downloaded}, 업로드=${result.uploaded}, 삭제=${result.deleted}, 스킵=${result.skipped}',
            tag: _tag
          );
        } catch (e) {
          final errorMsg = '$collection 동기화 실패: $e';
          LoggerUtils.logError(errorMsg, tag: _tag, error: e);
          errors.add(errorMsg);
          onError?.call(errorMsg);
        }
      }
      
      final result = DiffSyncResult(
        downloaded: totalDownloaded,
        uploaded: totalUploaded,
        deleted: totalDeleted,
        skipped: totalSkipped,
        errors: errors,
      );
      
      onProgress?.call('동기화 완료! (다운로드: $totalDownloaded, 업로드: $totalUploaded)');
      LoggerUtils.logInfo('행사 $eventId 차분 동기화 완료: $result', tag: _tag);
      
      return result;
    } catch (e) {
      LoggerUtils.logError('차분 동기화 실패', tag: _tag, error: e);
      onError?.call('동기화 실패: $e');
      rethrow;
    }
  }
  
  /// 특정 컬렉션의 차분 동기화
  Future<DiffSyncResult> _syncCollection(String userId, int eventId, String collectionName) async {
    try {
      // 1. 원격 데이터 개수 확인
      final remoteCollection = _firestore
          .collection('users')
          .doc(userId)
          .collection('events')
          .doc(eventId.toString())
          .collection(collectionName);

      final remoteSnapshot = await remoteCollection.count().get();
      final remoteCount = remoteSnapshot.count ?? 0;

      // 2. 동기화 필요 여부 확인
      final needsSync = await _metadataService.needsSync(eventId, collectionName, remoteCount);

      if (!needsSync) {
        LoggerUtils.logInfo('$collectionName: 동기화 불필요', tag: _tag);
        return const DiffSyncResult(downloaded: 0, uploaded: 0, deleted: 0, skipped: 1, errors: []);
      }

      // 3. 변경된 데이터만 가져오기
      final lastSync = await _metadataService.getLastSyncTime(eventId, collectionName) ??
                      DateTime.fromMillisecondsSinceEpoch(0);

      // Firebase에서 변경된 문서들 가져오기
      final remoteQuery = _firestore
          .collection('users')
          .doc(userId)
          .collection('events')
          .doc(eventId.toString())
          .collection(collectionName)
          .where('updatedAt', isGreaterThan: Timestamp.fromDate(lastSync))
          .orderBy('updatedAt');

      final remoteDocuments = await remoteQuery.get();

      // 4. 로컬 데이터와 비교하여 처리
      int downloaded = 0;
      int uploaded = 0;
      int deleted = 0;
      final List<String> errors = [];

      for (final doc in remoteDocuments.docs) {
        try {
          await _processDocumentChange(eventId, collectionName, doc);
          downloaded++;
        } catch (e) {
          errors.add('문서 ${doc.id} 처리 실패: $e');
        }
      }

      // 5. 마지막 동기화 시간 업데이트
      await _metadataService.updateLastSyncTime(
        eventId,
        collectionName,
        DateTime.now(),
        syncCount: remoteCount,
      );

      return DiffSyncResult(
        downloaded: downloaded,
        uploaded: uploaded,
        deleted: deleted,
        skipped: 0,
        errors: errors,
      );
    } catch (e) {
      LoggerUtils.logError('컬렉션 $collectionName 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  

  
  /// 문서 변경사항 처리
  Future<void> _processDocumentChange(int eventId, String collectionName, QueryDocumentSnapshot doc) async {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = int.parse(doc.id);
    
    switch (collectionName) {
      case 'categories':
        final category = Category.fromJson(data);
        await _databaseService.insertOrUpdateCategory(category);
        break;
      case 'products':
        final product = Product.fromJson(data);
        await _databaseService.insertOrUpdateProduct(product);
        break;
      case 'sellers':
        final seller = Seller.fromJson(data);
        await _databaseService.insertOrUpdateSeller(seller);
        break;
      case 'prepayments':
        final prepayment = Prepayment.fromJson(data);
        await _databaseService.insertOrUpdatePrepayment(prepayment);
        break;
      case 'prepayment_virtual_products':
        final virtualProduct = PrepaymentVirtualProduct.fromMap(data);
        await _databaseService.insertOrUpdatePrepaymentVirtualProduct(virtualProduct);
        break;
      case 'prepayment_product_links':
        final link = PrepaymentProductLink.fromMap(data);
        await _databaseService.insertOrUpdatePrepaymentProductLink(link);
        break;
      case 'sales_logs':
        final salesLog = SalesLog.fromJson(data);
        await _databaseService.insertOrUpdateSalesLog(salesLog);
        break;
    }
  }

  /// Firebase에서 행사 목록 가져오기
  Future<List<Event>> _getRemoteEvents(String userId) async {
    final eventsCollection = _firestore
        .collection('users')
        .doc(userId)
        .collection('events');

    final snapshot = await eventsCollection.get();

    return snapshot.docs.map((doc) {
      final data = doc.data();
      data['id'] = int.parse(doc.id);
      return Event.fromMap(data);
    }).toList();
  }

  /// 로컬에서 행사 목록 가져오기
  Future<List<Event>> _getLocalEvents() async {
    final db = await _databaseService.database;
    final result = await db.query('events');

    return result.map((map) => Event.fromMap(map)).toList();
  }
}
