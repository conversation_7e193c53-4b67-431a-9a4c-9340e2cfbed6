import 'package:freezed_annotation/freezed_annotation.dart';

part 'event.freezed.dart';
part 'event.g.dart';

/// 행사 정보를 표현하는 데이터 모델 클래스입니다.
/// - 행사명, 이미지, 시작/종료 날짜 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
/// - freezed를 사용하여 불변 객체로 생성
@freezed
abstract class Event with _$Event {
  const factory Event({
    int? id,
    required String name,
    String? imagePath,
    double? focusX,
    double? focusY,
    required DateTime startDate,
    required DateTime endDate,
    @Default(true) bool isActive,
    String? description,
    String? location,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) = _Event;

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);

  // SQLite 맵에서 직접 생성
  factory Event.fromMap(Map<String, dynamic> map) {
    return Event(
      id: map['id'],
      name: map['name'] ?? '',
      imagePath: map['imagePath'],
      focusX: map['focusX']?.toDouble(),
      focusY: map['focusY']?.toDouble(),
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      isActive: map['isActive'] == 1 || map['isActive'] == true,
      description: map['description'],
      location: map['location'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory Event.create({
    int? id,
    required String name,
    String? imagePath,
    double? focusX,
    double? focusY,
    required DateTime startDate,
    required DateTime endDate,
    bool isActive = true,
    String? description,
    String? location,
    DateTime? createdAt,
  }) {
    return Event(
      id: id,
      name: name,
      imagePath: imagePath,
      focusX: focusX,
      focusY: focusY,
      startDate: startDate,
      endDate: endDate,
      isActive: isActive,
      description: description,
      location: location,
      createdAt: createdAt ?? DateTime.now(),
    );
  }
}



// 날짜 관련 Extension
extension EventDate on Event {
  /// 행사가 현재 진행 중인지 확인
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate.add(const Duration(days: 1)));
  }

  /// 행사가 종료되었는지 확인
  bool get isEnded {
    final now = DateTime.now();
    return now.isAfter(endDate.add(const Duration(days: 1)));
  }

  /// 행사가 시작 전인지 확인
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startDate);
  }

  /// 행사 기간 문자열 반환 (예: "2024년 10월 13일 - 16일")
  String get dateRangeString {
    if (startDate.year == endDate.year && startDate.month == endDate.month) {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.day}일';
    } else if (startDate.year == endDate.year) {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.month}월 ${endDate.day}일';
    } else {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.year}년 ${endDate.month}월 ${endDate.day}일';
    }
  }

  /// 행사 상태 문자열 반환
  String get statusString {
    if (isUpcoming) return '예정';
    if (isOngoing) return '진행중';
    if (isEnded) return '종료';
    return '알 수 없음';
  }

  /// 행사 기간 (일 수)
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }
}

// SQLite 맵 변환을 위한 Extension
extension EventMapper on Event {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'name': name,
      'imagePath': imagePath,
      'focusX': focusX,
      'focusY': focusY,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive ? 1 : 0,
      'description': description,
      'location': location,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
    
    // id가 null이 아닌 경우에만 추가 (AUTOINCREMENT를 위해)
    if (id != null) {
      map['id'] = id;
    }
    
    return map;
  }
}
