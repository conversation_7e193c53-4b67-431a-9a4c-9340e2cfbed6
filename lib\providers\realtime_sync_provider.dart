/// 실시간 동기화 Provider (완전 작동 버전)
///
/// 새로운 RealtimeSyncService와 연동하여 실제로 작동하는
/// 실시간 동기화를 제공합니다.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/realtime_sync_service_main.dart';
import '../models/sync_state.dart';

/// 동기화 상태 데이터 클래스
class SyncStateData {
  final SyncState state;
  final String? message;
  final String? errorMessage;
  final bool isConnected;
  final int activeSubscriptions;

  const SyncStateData({
    this.state = SyncState.offline,
    this.message,
    this.errorMessage,
    this.isConnected = false,
    this.activeSubscriptions = 0,
  });

  SyncStateData copyWith({
    SyncState? state,
    String? message,
    String? errorMessage,
    bool? isConnected,
    int? activeSubscriptions,
  }) {
    return SyncStateData(
      state: state ?? this.state,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
      isConnected: isConnected ?? this.isConnected,
      activeSubscriptions: activeSubscriptions ?? this.activeSubscriptions,
    );
  }
}

/// 동기화 상태 관리 Notifier
class SyncStateNotifier extends StateNotifier<SyncStateData> {

  final RealtimeSyncService _syncService;

  SyncStateNotifier(this._syncService) : super(const SyncStateData());

  /// 초기화
  Future<void> initialize() async {
    try {
      await _syncService.initialize();

      // 상태 변화 리스너 등록
      _syncService.isConnected.addListener(_onConnectionChanged);
      _syncService.activeSubscriptions.addListener(_onSubscriptionsChanged);

      state = state.copyWith(
        state: SyncState.offline,
        isConnected: _syncService.isConnected.value,
        activeSubscriptions: _syncService.activeSubscriptions.value,
      );
    } catch (e) {
      state = state.copyWith(
        state: SyncState.error,
        errorMessage: e.toString(),
      );
    }
  }

  void _onConnectionChanged() {
    state = state.copyWith(
      isConnected: _syncService.isConnected.value,
      state: _syncService.isConnected.value ? SyncState.synced : SyncState.offline,
    );
  }

  void _onSubscriptionsChanged() {
    state = state.copyWith(
      activeSubscriptions: _syncService.activeSubscriptions.value,
    );
  }

  @override
  void dispose() {
    _syncService.isConnected.removeListener(_onConnectionChanged);
    _syncService.activeSubscriptions.removeListener(_onSubscriptionsChanged);
    super.dispose();
  }
}

/// 실시간 동기화 서비스 Provider
final realtimeSyncServiceProvider = Provider<RealtimeSyncService>((ref) {
  return RealtimeSyncService();
});

/// 동기화 상태 Provider
final syncStateProvider = StateNotifierProvider<SyncStateNotifier, SyncStateData>((ref) {
  final syncService = ref.watch(realtimeSyncServiceProvider);
  return SyncStateNotifier(syncService);
});